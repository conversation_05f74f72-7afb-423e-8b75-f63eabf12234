# PicoMudrasSequencer Documentation Overview

This document provides a comprehensive, unified overview of the PicoMudrasSequencer project documentation. It is designed with a modern educational UX style, ensuring clarity, consistency, and ease of navigation.

---

## Project Summary

The PicoMudrasSequencer is a dual-voice polyrhythmic step sequencer built for the Raspberry Pi Pico2. It integrates real-time audio synthesis, gesture-based control via distance sensors and magnetic encoders, and an expressive LED matrix for visual feedback.

---

## Documentation Structure

### 1. Root README.md

- Provides a high-level project overview, philosophy, core features, architecture, MIDI implementation, hardware requirements, and getting started instructions.
- Includes detailed parameter recording workflow and key external libraries.

### 2. Module README Files

Each core module has a dedicated README file that covers:

- Module overview and purpose
- Key features and functionality
- API reference with usage examples
- Dependencies and related modules

Modules with README files include:

- LEDMatrix
- Matrix (button matrix input)
- MIDI Manager
- Sensors
- Sequencer
- UI

### 3. API and Source Code Documentation

- Helper functions and core classes are documented inline in source files.
- Key functions like `mapNormalizedValueToParamRange` are documented for clarity.
- Source code comments provide implementation insights.

### 4. Additional Documentation

- Files in the `docs/` directory include MIDI CC specifications and implementation redesign documents for deeper technical understanding.

---

## Style and Formatting Guidelines

- Use clear, concise language with consistent terminology.
- Employ Markdown formatting for headings, lists, code blocks, and tables.
- Include diagrams and flowcharts where helpful.
- Provide usage examples for APIs and modules.
- Maintain cross-references between documents for easy navigation.

---

## How to Use This Documentation

- Start with the root README.md for a broad understanding.
- Dive into module README files for detailed information on specific components.
- Refer to source code comments and API documentation for development and debugging.
- Consult additional docs for advanced topics and specifications.

---

This overview will be maintained and updated to ensure all documentation remains comprehensive and user-friendly.