#include "ParameterManager.h"
#include <algorithm> // For std::max, std::min
#include <cmath>     // For roundf
#include <random>    // For std::default_random_engine, std::uniform_real_distribution
#include <chrono>    // For std::chrono::system_clock (for seeding)
#include <variant>   // For std::visit
#include "../sensors/as5600.h" // For AS5600ParameterMode
#include "../sensors/AS5600Manager.h" // For MAX_DELAY_SAMPLES extern declaration

// AS5600 parameter bounds management functions moved to src/sensors/AS5600Manager.cpp

// Helper function to safely get float from ParameterValueType variant
// This function is internal to ParameterManager.cpp
static float getFloatFromParameterValueType(const ParameterValueType &v)
{
    return std::visit([](auto &&arg) -> float
                      {
                          using T = std::decay_t<decltype(arg)>;
                          if constexpr (std::is_same_v<T, float>)
                              return arg;
                          if constexpr (std::is_same_v<T, int>)
                              return static_cast<float>(arg);
                          if constexpr (std::is_same_v<T, bool>)
                              return arg ? 1.0f : 0.0f;
                          return 0.0f; // Fallback for unexpected types
                      },
                      v);
}

ParameterManager::ParameterManager()
{
    // Initialize the spin lock in the constructor
    _lock = spin_lock_init(spin_lock_claim_unused(true)); // Claim a unique lock number
}

void ParameterManager::init()
{
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i)
    {
        // Initialize each track with its default value from CORE_PARAMETERS
        _tracks[i].init(getFloatFromParameterValueType(CORE_PARAMETERS[i].defaultValue));
    }
}

void ParameterManager::setStepCount(ParamId id, uint8_t steps)
{
    // Slide parameter should always operate at full 16-step length
    // and cannot be changed by the parameter length adjustment workflow
    if (id == ParamId::Slide) {
        _tracks[static_cast<size_t>(id)].resize(16);
        return;
    }

    _tracks[static_cast<size_t>(id)].resize(steps);
}

uint8_t ParameterManager::getStepCount(ParamId id) const
{
    uint8_t count = _tracks[static_cast<size_t>(id)].stepCount;
    return count;
}

float ParameterManager::getValue(ParamId id, uint8_t stepIdx) const
{
    float value = _tracks[static_cast<size_t>(id)].getValue(stepIdx);
    return value;
}

void ParameterManager::setValue(ParamId id, uint8_t stepIdx, float value)
{

    // Apply clamping and rounding based on parameter definition
    const auto &paramDef = CORE_PARAMETERS[static_cast<size_t>(id)];
    float minVal = getFloatFromParameterValueType(paramDef.minValue);
    float maxVal = getFloatFromParameterValueType(paramDef.maxValue);

    float clampedValue = std::max(minVal, std::min(value, maxVal));

    if (paramDef.isBinary)
    { // For boolean parameters, round to 0 or 1
        clampedValue = (clampedValue > 0.5f) ? 1.0f : 0.0f;
    }
    else if (paramDef.minValue.index() == 0)
    { // If min value is int, assume integer parameter
        clampedValue = roundf(clampedValue);
    }

    // DEBUG: Trace parameter setting (only for Note parameter to reduce spam)
    /*
    if (id == ParamId::Note) {
        Serial.print("[PARAM SET DEBUG] Note step ");
        Serial.print(stepIdx);
        Serial.print(": ");
        Serial.print(value, 2);
        Serial.print(" -> ");
        Serial.print(clampedValue, 2);
        Serial.print(" (range: ");
        Serial.print(minVal, 2);
        Serial.print("-");
        Serial.print(maxVal, 2);
        Serial.println(")");
    }
    */

    _tracks[static_cast<size_t>(id)].setValue(stepIdx, clampedValue);
}
void ParameterManager::randomizeParameters()
{
    // Use a better random number generator
    static std::default_random_engine generator(std::chrono::system_clock::now().time_since_epoch().count());

    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i)
    {
        ParamId currentParamId = static_cast<ParamId>(i);

        // When randomizing, ensure the Slide parameter's length is set to max
        if (currentParamId == ParamId::Slide)
        {
            _tracks[i].stepCount = 16;
        }

        const auto &paramDef = CORE_PARAMETERS[i];
        float minVal = getFloatFromParameterValueType(paramDef.minValue);
        float maxVal = getFloatFromParameterValueType(paramDef.maxValue);
        std::uniform_real_distribution<float> distribution(minVal, maxVal);

        for (uint8_t step = 0; step < _tracks[i].stepCount; ++step)
        {
            // For slide and gate, we want a 10% chance of being 1, otherwise 0
            if (currentParamId == ParamId::Slide)
            {
                std::uniform_int_distribution<int> slideChanceDist(0, 12);
                int slideStepValue = (slideChanceDist(generator) == 0) ? 1 : 0;
                _tracks[i].setValue(step, slideStepValue);
            }
            else if (currentParamId == ParamId::Gate)
            {
                if ((step % 2) == 0) // If the step is even
                {
                    // 75% chance of being 1, otherwise 0
                    std::uniform_int_distribution<int> gateEvenChanceDist(0, 3);   // 0, 1, 2 for 1; 3 for 0
                    int gateEvenStepValue = (gateEvenChanceDist(generator) == 0) ? 0 : 1; // Corrected logic for 75%
                    _tracks[i].setValue(step, gateEvenStepValue);
                }
                else
                {
                    std::uniform_int_distribution<int> gateOddChanceDist(0, 2);
                    int gateOddStepValue = (gateOddChanceDist(generator) == 0) ? 1 : 0;
                    _tracks[i].setValue(step, gateOddStepValue);
                }
            }
            else if (currentParamId == ParamId::Filter)
            {
                std::uniform_real_distribution<float> filterDist(0.2f, 0.7f);
                float filterValue = filterDist(generator);
                _tracks[i].setValue(step, filterValue);
            }
            else if (currentParamId == ParamId::Attack)
            {
                std::uniform_real_distribution<float> attackDist(0.0f, 0.15f);
                float attackValue = attackDist(generator);
                _tracks[i].setValue(step, attackValue);
            }
            else if (currentParamId == ParamId::Decay)
            {
                std::uniform_real_distribution<float> decayDist(0.01f, 0.75f);
                float decayValue = decayDist(generator);
                _tracks[i].setValue(step, decayValue);
            }
            else
            {
                float randomValue = distribution(generator);
                _tracks[i].setValue(step, randomValue);
            }
        }
    }
}
