#include "Sequencer.h"
#include "SequencerDefs.h"
#include <cstddef>
#include <variant> // For std::visit
#include <algorithm>

/**
 * @brief Map a normalized value in the range [0.0, 1.0] to the specific parameter's value range.
 *
 * This helper function converts a normalized floating-point input into the actual parameter value,
 * based on the parameter's defined minimum and maximum limits. It supports parameters with
 * underlying types of float, int, or bool.
 *
 * @param id The identifier of the parameter (ParamId enum).
 * @param normalizedValue A float value normalized between 0.0 and 1.0.
 * @return The mapped parameter value as a float within the parameter's valid range.
 *
 * @note Clamping and rounding are handled separately by the ParameterManager's setValue method.
 */
float mapNormalizedValueToParamRange(ParamId id, float normalizedValue)
{
    const auto &def = CORE_PARAMETERS[static_cast<size_t>(id)];

    // Safely get float from the variant, regardless of underlying type (int, float, bool)
    auto get_float = [](const ParameterValueType &v) -> float
    {
        return std::visit(
            [](auto &&arg) -> float
            {
                using T = std::decay_t<decltype(arg)>;
                if constexpr (std::is_same_v<T, float>) return arg;
                if constexpr (std::is_same_v<T, int>) return static_cast<float>(arg);
                if constexpr (std::is_same_v<T, bool>) return arg ? 1.0f : 0.0f;
                return 0.0f; // Fallback
            },
            v);
    };

    float minVal = get_float(def.minValue);
    float maxVal = get_float(def.maxValue);

    // The ParameterManager's setValue will handle clamping and rounding.
    return minVal + normalizedValue * (maxVal - minVal);
}

// Helper function to map float octave value to semitone offset
int mapFloatToOctaveOffset(float octaveValue) {
    // Map 0.0-1.0 to -24 to +24 semitones (2 octaves up/down)
    return static_cast<int>((octaveValue - 0.5f) * 48.0f);
}

// =======================
//   SEQUENCER CLASS IMPLEMENTATION
// =======================

Sequencer::Sequencer() : Sequencer(1) {}

Sequencer::Sequencer(uint8_t ch)
    : running(false), currentStep(0), lastNote(-1), currentNote(-1),
      noteDurationCounter(0), channel(ch), previousStepHadSlide(false),
      lfo1Target(ParamId::Count), lfo2Target(ParamId::Count)
{
    // Initialize all parameter step counters to 0
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
        currentStepPerParam[i] = 0;
    }

    initializeParameters();
}

void Sequencer::resetSequencer() {
    running = false;
    currentStep = 0;
    lastNote = -1;
    currentNote = -1;
    noteDurationCounter = 0;
    previousStepHadSlide = false;

    // Reset all parameter step counters
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
        currentStepPerParam[i] = 0;
    }

    envelope.release();
    noteDuration.reset();
}

void Sequencer::initializeParameters() {
    parameterManager.init();
}

void Sequencer::resetAllSteps() {
    parameterManager.init();
}

void Sequencer::randomizeParameters() {
    parameterManager.randomizeParameters();
}

// Parameter access methods
float Sequencer::getStepParameterValue(ParamId id, uint8_t stepIdx) const {
    return parameterManager.getValue(id, stepIdx);
}

void Sequencer::setStepParameterValue(ParamId id, uint8_t stepIdx, float value) {
    parameterManager.setValue(id, stepIdx, value);
}

uint8_t Sequencer::getParameterStepCount(ParamId id) const {
    return parameterManager.getStepCount(id);
}

void Sequencer::setParameterStepCount(ParamId id, uint8_t steps) {
    parameterManager.setStepCount(id, steps);
}

void Sequencer::reset() {
    resetSequencer();
}

uint8_t Sequencer::getCurrentStepForParameter(ParamId paramId) const {
    return currentStepPerParam[static_cast<size_t>(paramId)];
}

void Sequencer::assignLFO(uint8_t lfo, ParamId paramId) {
    if (lfo == 1) {
        lfo1Target = paramId;
    } else if (lfo == 2) {
        lfo2Target = paramId;
    }
}

Step Sequencer::getStep(uint8_t stepIdx) const {
    Step step;
    step.gate = getStepParameterValue(ParamId::Gate, stepIdx) > 0.5f;
    step.note = getStepParameterValue(ParamId::Note, stepIdx);
    step.velocity = getStepParameterValue(ParamId::Velocity, stepIdx);
    step.filter = getStepParameterValue(ParamId::Filter, stepIdx);
    step.attack = getStepParameterValue(ParamId::Attack, stepIdx);
    step.decay = getStepParameterValue(ParamId::Decay, stepIdx);
    step.octave = getStepParameterValue(ParamId::Octave, stepIdx);
    step.gateLength = static_cast<uint16_t>(getStepParameterValue(ParamId::GateLength, stepIdx));
    step.slide = getStepParameterValue(ParamId::Slide, stepIdx) > 0.5f;
    return step;
}

void Sequencer::toggleStep(uint8_t stepIdx) {
    float currentGate = getStepParameterValue(ParamId::Gate, stepIdx);
    setStepParameterValue(ParamId::Gate, stepIdx, currentGate > 0.5f ? 0.0f : 1.0f);
}

void Sequencer::setMidiNoteOffCallback(void (*callback)(uint8_t note, uint8_t channel)) {
    midiNoteOffCallback = callback;
}

void Sequencer::triggerEnvelope() {
    envelope.trigger();
}

void Sequencer::releaseEnvelope() {
    envelope.release();
}

// Note/Envelope handling methods
void Sequencer::startNote(uint8_t note, uint8_t velocity, uint16_t duration) {
    currentNote = note;
    noteDuration.start(duration);
    triggerEnvelope();
}

void Sequencer::handleNoteOff(VoiceState* voiceState) {
    if (voiceState) {
        voiceState->gate = false;
        voiceState->retrigger = false;
    }
    releaseEnvelope();

    // Send MIDI note off if callback is set
    if (midiNoteOffCallback && currentNote >= 0) {
        midiNoteOffCallback(static_cast<uint8_t>(currentNote), channel);
    }

    lastNote = currentNote;
    currentNote = -1;
}

void Sequencer::tickNoteDuration(VoiceState* voiceState) {
    noteDuration.tick();
    if (!noteDuration.isActive() && voiceState && voiceState->gate) {
        handleNoteOff(voiceState);
    }
}

bool Sequencer::isNotePlaying() const {
    return noteDuration.isActive() && currentNote >= 0;
}

void Sequencer::playStepNow(uint8_t stepIdx, VoiceState* voiceState, float lfo1Value, float lfo2Value) {
    if (!voiceState) return;

    processStep(stepIdx, voiceState, lfo1Value, lfo2Value);
}

void Sequencer::advanceStep(uint8_t current_uclock_step, int mm_distance,
                           const UIState& uiState, VoiceState *voiceState,
                           float lfo1Value, float lfo2Value) {
    if (!running || !voiceState) return;

    // Update global step counter
    currentStep = current_uclock_step;

    // Calculate independent step positions for each parameter
    for (size_t i = 0; i < static_cast<size_t>(ParamId::Count); ++i) {
        ParamId paramId = static_cast<ParamId>(i);
        uint8_t stepCount = getParameterStepCount(paramId);
        if (stepCount > 0) {
            currentStepPerParam[i] = current_uclock_step % stepCount;
        } else {
            currentStepPerParam[i] = 0;
        }
    }

    // Handle real-time parameter recording if any parameter buttons are held
    if (mm_distance > 0) { // Valid sensor reading
        float normalizedDistance = static_cast<float>(mm_distance) / 400.0f; // Normalize to 0.0-1.0

        // Check each parameter button and record if held
        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Note)]) { // Note button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Note)];
            float noteValue = mapNormalizedValueToParamRange(ParamId::Note, normalizedDistance);
            setStepParameterValue(ParamId::Note, stepToRecord, noteValue);
        }

        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Velocity)]) { // Velocity button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Velocity)];
            float velocityValue = mapNormalizedValueToParamRange(ParamId::Velocity, normalizedDistance);
            setStepParameterValue(ParamId::Velocity, stepToRecord, velocityValue);
        }

        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Filter)]) { // Filter button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Filter)];
            float filterValue = mapNormalizedValueToParamRange(ParamId::Filter, normalizedDistance);
            setStepParameterValue(ParamId::Filter, stepToRecord, filterValue);
        }

        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Attack)]) { // Attack button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Attack)];
            float attackValue = mapNormalizedValueToParamRange(ParamId::Attack, normalizedDistance);
            setStepParameterValue(ParamId::Attack, stepToRecord, attackValue);
        }

        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Decay)]) { // Decay button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Decay)];
            float decayValue = mapNormalizedValueToParamRange(ParamId::Decay, normalizedDistance);
            setStepParameterValue(ParamId::Decay, stepToRecord, decayValue);
        }

        if (uiState.parameterButtonHeld[static_cast<size_t>(ParamId::Octave)]) { // Octave button
            uint8_t stepToRecord = currentStepPerParam[static_cast<size_t>(ParamId::Octave)];
            float octaveValue = mapNormalizedValueToParamRange(ParamId::Octave, normalizedDistance);
            setStepParameterValue(ParamId::Octave, stepToRecord, octaveValue);
        }
    }

    // Process the current step using independent parameter positions
    processStep(currentStep, voiceState, lfo1Value, lfo2Value);
}

void Sequencer::processStep(uint8_t stepIdx, VoiceState* voiceState, float lfo1Value, float lfo2Value) {
    if (!voiceState) return;

    // Get parameter values using independent step positions
    bool gateOn = getStepParameterValue(ParamId::Gate, currentStepPerParam[static_cast<size_t>(ParamId::Gate)]) > 0.5f;
    float noteValue = getStepParameterValue(ParamId::Note, currentStepPerParam[static_cast<size_t>(ParamId::Note)]);
    float velocityValue = getStepParameterValue(ParamId::Velocity, currentStepPerParam[static_cast<size_t>(ParamId::Velocity)]);
    float filterValue = getStepParameterValue(ParamId::Filter, currentStepPerParam[static_cast<size_t>(ParamId::Filter)]);
    float attackValue = getStepParameterValue(ParamId::Attack, currentStepPerParam[static_cast<size_t>(ParamId::Attack)]);
    float decayValue = getStepParameterValue(ParamId::Decay, currentStepPerParam[static_cast<size_t>(ParamId::Decay)]);
    float octaveValue = getStepParameterValue(ParamId::Octave, currentStepPerParam[static_cast<size_t>(ParamId::Octave)]);
    float gateLengthValue = getStepParameterValue(ParamId::GateLength, currentStepPerParam[static_cast<size_t>(ParamId::GateLength)]);
    bool slideOn = getStepParameterValue(ParamId::Slide, currentStepPerParam[static_cast<size_t>(ParamId::Slide)]) > 0.5f;

    // Apply LFO modulation if assigned
    if (lfo1Target != ParamId::Count) {
        switch (lfo1Target) {
            case ParamId::Filter: filterValue += lfo1Value * 0.2f; break;
            case ParamId::Attack: attackValue += lfo1Value * 0.1f; break;
            case ParamId::Decay: decayValue += lfo1Value * 0.1f; break;
            case ParamId::Velocity: velocityValue += lfo1Value * 0.2f; break;
            default: break;
        }
    }

    if (lfo2Target != ParamId::Count) {
        switch (lfo2Target) {
            case ParamId::Filter: filterValue += lfo2Value * 0.2f; break;
            case ParamId::Attack: attackValue += lfo2Value * 0.1f; break;
            case ParamId::Decay: decayValue += lfo2Value * 0.1f; break;
            case ParamId::Velocity: velocityValue += lfo2Value * 0.2f; break;
            default: break;
        }
    }

    // Clamp values after LFO modulation
    filterValue = std::max(0.0f, std::min(1.0f, filterValue));
    attackValue = std::max(0.0f, std::min(1.0f, attackValue));
    decayValue = std::max(0.0f, std::min(1.0f, decayValue));
    velocityValue = std::max(0.0f, std::min(1.0f, velocityValue));

    // Calculate note duration in ticks
    uint16_t noteDurationTicks = std::max(1, static_cast<int>(gateLengthValue * PULSES_PER_SEQUENCER_STEP));

    // Map octave to semitone offset
    int octaveOffset = mapFloatToOctaveOffset(octaveValue);

    // Update voice state
    voiceState->gate = gateOn;
    voiceState->note = noteValue;
    voiceState->velocity = velocityValue;
    voiceState->filter = filterValue;
    voiceState->attack = attackValue;
    voiceState->decay = decayValue;
    voiceState->octave = octaveValue;
    voiceState->gateLength = static_cast<uint16_t>(gateLengthValue * 1000.0f); // Convert to milliseconds
    voiceState->slide = slideOn;
    voiceState->retrigger = false;

    // Handle note triggering and slide logic
    if (gateOn) {
        if (slideOn && previousStepHadSlide && isNotePlaying()) {
            // Slide: update note without retriggering
            currentNote = static_cast<int8_t>(noteValue * 127.0f) + octaveOffset;
            noteDuration.start(noteDurationTicks); // Extend duration
        } else {
            // Normal trigger or slide start
            voiceState->retrigger = true;
            int8_t newNote = static_cast<int8_t>(noteValue * 127.0f) + octaveOffset;
            startNote(newNote, static_cast<uint8_t>(velocityValue * 127.0f), noteDurationTicks);
        }
    } else {
        // Gate off
        if (isNotePlaying()) {
            handleNoteOff(voiceState);
        }
    }

    previousStepHadSlide = slideOn;
}
